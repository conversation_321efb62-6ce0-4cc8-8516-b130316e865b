from typing import List

# https://docs.crewai.com/en/
from crewai import Agent, Crew, Process, Task
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task
from crewai.agents.agent_builder.base_agent import BaseAgent


from lmstudioconfig import LMStudioConfig
from tools.custom_tool import ReadHtmlPageTool


# If you want to run a snippet of code before or after the crew starts,
# you can use the @before_kickoff and @after_kickoff decorators
# https://docs.crewai.com/concepts/crews#example-crew-class-with-decorators

@CrewBase
class AiEntities():
    """AiEntities crew"""

    agents: List[BaseAgent]
    tasks: List[Task]
    agents_config: dict
    tasks_config: dict

    llm = LMStudioConfig.create_llm()

    # Learn more about YAML configuration files here:
    # Agents: https://docs.crewai.com/concepts/agents#yaml-configuration-recommended
    # Tasks: https://docs.crewai.com/concepts/tasks#yaml-configuration-recommended

    # If you would like to add tools to your agents, you can learn more about it here:
    # https://docs.crewai.com/concepts/agents#agent-tools
    @agent
    def text_reader(self) -> Agent:
        return Agent(
            config=self.agents_config['text_reader'], # type: ignore[index]
            llm=self.llm,
            tools=[ReadHtmlPageTool()],
            verbose=True
        )

    # To learn more about structured task outputs,
    # task dependencies, and task callbacks, check out the documentation:
    # https://docs.crewai.com/concepts/tasks#overview-of-a-task
    @task
    def read_text(self) -> Task:
        return Task(
            config=self.tasks_config['read_text'], # type: ignore[index]
        )

    @crew
    def crew(self) -> Crew:
        """Creates the AiEntities crew"""
        # To learn how to add knowledge sources to your crew, check out the documentation:
        # https://docs.crewai.com/concepts/knowledge#what-is-knowledge

        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
        )
