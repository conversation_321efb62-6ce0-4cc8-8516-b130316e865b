
#
import httpx

# https://github.com/adbar/trafilatura
from trafilatura import extract

# https://docs.crewai.com/en/
# Example of CrewAI tools : https://github.com/crewAIInc/crewAI-tools
from crewai.tools import tool

def fetch_url(url: str) -> str:
    """
    Fetch content from a URL using httpx.

    Args:
        url (str): The URL to fetch
        console (Console): Rich console instance for output

    Returns:
        str: The response content as text
    """
    try:
        with httpx.Client() as client:
            response = client.get(
                url,
                headers={"User-Agent": "Mozilla/5.0"},
                timeout=30.0,
                follow_redirects=True,
            )
            response.raise_for_status()

        page_raw = response.text
        content_type = response.headers.get("content-type", "")

        is_page_html = (
            "<html" in page_raw[:100] or "text/html" in content_type or not content_type
        )

        if is_page_html:
            return page_raw

        return ""
    except httpx.RequestError as e:
        return(f"❌ Erreur réseau lors de la requête vers {url}: {e}")
    except httpx.HTTPStatusError as e:
        return(f"❌ Erreur HTTP {e.response.status_code} lors de la requête vers {url}")

@tool("read_html_page")
def read_html_page(url: str) -> str:
    """
    Extract clean text content from a web page URL.

    Fetches HTML content from the specified URL and extracts the main text
    content using trafilatura, removing HTML tags, comments, and other markup.

    Args:
        url (str): The URL of the web page to read and extract content from

    Returns:
        str: Clean text content extracted from the web page, or empty string if extraction fails
    """
    html_content = fetch_url(url)
    content = extract(html_content, include_comments=False, favor_precision=True)
    return content if content is not None else ""
