
from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
from .html_tools import read_html_page


class ReadHtmlPageToolInput(BaseModel):
    """Input schema for MyCustomTool."""
    url: str = Field(..., description="URL de la page HTML à lire et extraire.")

class ReadHtmlPageTool(BaseTool):
    name: str = "read_html_page"
    description: str = (
        "Outil pour lire et extraire le contenu textuel d'une page HTML à partir d'une URL. "
        "Utilise trafilatura pour nettoyer le HTML et extraire uniquement le texte principal."
    )
    args_schema: Type[BaseModel] = ReadHtmlPageToolInput

    def _run(self, url: str) -> str:
        """Exécute la lecture et l'extraction du contenu HTML."""
        return "TEST" #len(read_html_page(url))
