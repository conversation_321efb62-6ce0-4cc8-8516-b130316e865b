from typing import List

# https://github.com/psf/requests
import requests

# https://docs.crewai.com/en/
from crewai.llm import LLM


class LMStudioConfig:
    """Configuration pour LM Studio"""

    BASE_URL = "http://localhost:1234/v1"
    DEFAULT_MODEL = "lm-studio"  # Modèle par défaut

    @classmethod
    def set_base_url(cls, url: str):
        """Définit l'URL de base pour LM Studio."""
        if url:
            cls.BASE_URL = url

    @classmethod
    def get_available_models(cls) -> List[str]:
        """Récupère la liste des modèles disponibles depuis LM Studio"""
        try:
            response = requests.get(f"{cls.BASE_URL}/models", timeout=5)
            if response.status_code == 200:
                models_data = response.json()
                return [model["id"] for model in models_data.get("data", [])]
        except requests.RequestException as e:
            print(f"Impossible de récupérer les modèles LM Studio: {e}")

        return []

    @classmethod
    def create_llm(
        cls, model_name: str = "", temperature: float = 0.7, max_tokens: int = 4000
    ) -> LLM:
        """Crée une instance LLM configurée pour LM Studio"""
        if not model_name:
            model_name = f"lm_studio/{cls.DEFAULT_MODEL}"

        return LLM(
            model=model_name,  # "lm_studio/" + cls.DEFAULT_MODEL,
            base_url=cls.BASE_URL,
            temperature=temperature,
            max_tokens=max_tokens,
            api_key="lm-studio",
        )

