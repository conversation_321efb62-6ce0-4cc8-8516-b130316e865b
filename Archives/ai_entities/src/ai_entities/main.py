#!/usr/bin/env python
import warnings

from datetime import datetime

from crew import AiEntities

warnings.filterwarnings("ignore", category=SyntaxWarning, module="pysbd")


def run():
    """
    Run the crew.
    """
    inputs = {
        "url": "https://zonetuto.fr/intelligence-artificielle/generation-gratuit-image-openrouter-nano-banana/",
        "current_year": str(datetime.now().year)
    }

    AiEntities().crew().kickoff(inputs=inputs)

    '''crew_instance = AiEntities()
    read_text_task = crew_instance.read_text()
    text_reader_agent = crew_instance.text_reader()
    result = text_reader_agent.execute_task(read_text_task)'''

if __name__ == "__main__":
    run()

# https://github.com/crewAIInc/crewAI-examples
