#!/usr/bin/env python
"""
Exemple d'utilisation du lecteur de page HTML

Ce script montre différentes façons d'utiliser votre agent lecteur de page HTML.
"""

import sys
import os

# Ajouter le chemin src au PYTHONPATH pour les imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'ai_entities'))

from src.ai_entities.tools.html_tools import read_html_page

def example_direct_tool_usage():
    """Exemple d'utilisation directe de l'outil HTML"""
    print("=== Utilisation directe de l'outil ===")
    
    url = "https://example.com"
    print(f"Lecture de: {url}")
    
    content = read_html_page(url)
    print(f"Contenu extrait ({len(content)} caractères):")
    print(content[:500] + "..." if len(content) > 500 else content)

def example_with_user_input():
    """Exemple avec saisie utilisateur"""
    print("\n=== Utilisation avec saisie utilisateur ===")
    
    url = input("Entrez l'URL à lire: ").strip()
    if not url:
        print("Aucune URL fournie.")
        return
    
    if not url.startswith(('http://', 'https://')):
        print("L'URL doit commencer par http:// ou https://")
        return
    
    print(f"Lecture de: {url}")
    content = read_html_page(url)
    
    if content:
        print(f"\n✅ Contenu extrait ({len(content)} caractères):")
        print("-" * 50)
        print(content[:1000] + "..." if len(content) > 1000 else content)
    else:
        print("❌ Impossible d'extraire le contenu de cette page.")

if __name__ == "__main__":
    print("🤖 Exemples d'utilisation du lecteur de page HTML")
    print("=" * 50)
    
    # Exemple 1: Utilisation directe
    example_direct_tool_usage()
    
    # Exemple 2: Avec saisie utilisateur
    example_with_user_input()
