#!/usr/bin/env python
"""
Test simple du lecteur HTML
"""

import sys
import os

# Ajouter le chemin pour les imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'ai_entities'))

try:
    from tools.html_tools import read_html_page
    
    print("🧪 Test du lecteur HTML")
    print("=" * 30)
    
    # Test avec une URL simple
    test_url = "https://httpbin.org/html"
    print(f"📖 Test avec: {test_url}")
    
    content = read_html_page(test_url)
    
    if content:
        print(f"✅ Succès! Contenu extrait ({len(content)} caractères):")
        print("-" * 30)
        print(content[:300] + "..." if len(content) > 300 else content)
    else:
        print("❌ Aucun contenu extrait")
        
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    print("Assurez-vous d'être dans le bon répertoire et que les dépendances sont installées.")
except Exception as e:
    print(f"❌ Erreur: {e}")
