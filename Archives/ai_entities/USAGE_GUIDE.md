# Guide d'utilisation - Lecteur de page HTML

Ce guide explique comment utiliser votre agent lecteur de page HTML pour extraire le contenu textuel de pages web.

## 🚀 Méthodes pour fournir une URL

### Méthode 1: Ligne de commande
```bash
cd ai_entities
python src/ai_entities/main.py "https://example.com"
```

### Méthode 2: Saisie interactive
```bash
cd ai_entities
python src/ai_entities/main.py
# Le système vous demandera d'entrer l'URL
```

### Méthode 3: Utilisation avec CrewAI
```bash
cd ai_entities
crewai run
# Modifiez d'abord l'URL dans main.py si nécessaire
```

### Méthode 4: Utilisation directe de l'outil
```python
from tools.html_tools import read_html_page

url = "https://example.com"
content = read_html_page(url)
print(content)
```

## 📋 Exemples d'utilisation

### Exemple 1: URL simple
```bash
python src/ai_entities/main.py "https://wikipedia.org"
```

### Exemple 2: Article de blog
```bash
python src/ai_entities/main.py "https://medium.com/@author/article-title"
```

### Exemple 3: Page de documentation
```bash
python src/ai_entities/main.py "https://docs.python.org/3/"
```

## 🛠️ Fonctionnalités

- **Extraction automatique**: Utilise trafilatura pour extraire uniquement le contenu principal
- **Nettoyage HTML**: Supprime automatiquement les balises, commentaires et éléments de navigation
- **Gestion d'erreurs**: Affiche des messages d'erreur clairs en cas de problème
- **Support multi-format**: Fonctionne avec la plupart des pages HTML standard

## 🔧 Configuration

### Modifier l'URL par défaut
Éditez le fichier `src/ai_entities/main.py` ligne 24:
```python
url = "VOTRE_URL_ICI"
```

### Personnaliser l'agent
Modifiez `src/ai_entities/config/agents.yaml` pour changer le comportement de l'agent.

### Personnaliser la tâche
Modifiez `src/ai_entities/config/tasks.yaml` pour ajuster la description de la tâche.

## 📝 Format de sortie

L'agent retourne le contenu textuel nettoyé de la page, prêt pour:
- Analyse de contenu
- Résumé automatique
- Extraction d'informations
- Traitement par d'autres agents

## ⚠️ Limitations

- Nécessite une connexion internet
- Certaines pages avec beaucoup de JavaScript peuvent ne pas être entièrement extraites
- Respecte les timeouts (30 secondes par défaut)
- Suit les redirections automatiquement

## 🐛 Dépannage

### Erreur d'import
Si vous obtenez une erreur d'import, assurez-vous d'être dans le bon répertoire:
```bash
cd ai_entities
export PYTHONPATH=$PYTHONPATH:$(pwd)/src/ai_entities
```

### Erreur de réseau
Vérifiez votre connexion internet et que l'URL est accessible.

### Page vide
Certaines pages nécessitent JavaScript. Essayez avec une URL différente pour tester.
