[project]
name = "ai_entities"
version = "0.1.0"
description = "ai_entities using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.14"
dependencies = [
    "crewai[tools]>=0.186.1,<1.0.0"
]

[project.scripts]
ai_entities = "ai_entities.main:run"
run_crew = "ai_entities.main:run"
train = "ai_entities.main:train"
replay = "ai_entities.main:replay"
test = "ai_entities.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
