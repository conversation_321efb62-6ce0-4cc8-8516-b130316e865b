name: entity-analysis-pipeline

description: >
  Workflow multi-agents CrewAI pour analyser un texte :
  - ingestion d’une page HTML,
  - extraction d’entités avec Wikidata,
  - génération d’un résumé des entités principales.

model: lm_studio/qwen/qwen3-4b-2507

agents:
  - id: text-reader
    role: Lecteur de page HTML
    goal: Charger le contenu textuel d’une page HTML pour l’analyse.
    backstory: >
      Cet agent lit une URL HTML, extrait le texte brut et le prépare
      pour les autres agents de la pipeline.
    tools:
      - read_html_page

  - id: wikidata-entity-extractor
    role: Expert en extraction d’entités nommées et désambiguïsation Wikidata
    goal: Identifier toutes les entités dans un texte, les mapper avec Wikidata et calculer un score d’importance.
    backstory: >
      Cet agent est spécialisé en NLP et en désambiguïsation d’entités.
      Il utilise exclusivement l’outil `search_wikidata_term` pour interroger Wikidata.
      Il génère toujours un JSON unique et structuré, sans explication additionnelle.
    tools:
      - search_wikidata_term

  - id: summarizer
    role: Résumeur d’entités
    goal: Produire un résumé narratif des entités les plus importantes extraites par l’agent Wikidata.
    backstory: >
      Cet agent transforme les données JSON structurées en un texte synthétique et lisible,
      pour faciliter l’interprétation humaine des résultats.
    tools: []

tasks:
  - id: read_text
    name: Lire et extraire une page HTML
    description: >
      Utilise l’outil `read_html_page` pour télécharger et extraire le texte brut
      d’une page HTML à partir de l’URL fournie : {{url}}
    agent: text-reader
    expected_output: >
      Un texte brut nettoyé, prêt à être transmis à l’agent d’extraction.

  - id: extract_entities
    name: Extraction et désambiguïsation d’entités
    description: |
      Vous êtes un expert en extraction d’entités nommées et en désambiguïsation avec Wikidata.

      Votre tâche est de :

      1. Identifier toutes les entités clés présentes dans le texte fourni. Ne supprime pas les entités n'ayant pas d'identifiant Wikidata.

      2. Pour chaque entité, récupérer via l’outil `search_wikidata_term` :
         - son identifiant Wikidata (Qxxx),
         - son label,
         - sa description,
         - son type,
         - le nombre de sitelinks.

      3. Calculer un score d’importance pour chaque entité selon la règle suivante :
         - F = fréquence (1 point par occurrence)
         - P = (S / S_max) * 100
         - B = +50 si Wikidata ID existe
         - Score final = F + P + B

      4. Retourner un JSON structuré, sans texte supplémentaire.

      Texte à analyser : {{read_text.output}}
    agent: wikidata-entity-extractor
    expected_output: >
      Un JSON unique structuré contenant toutes les entités extraites,
      leurs métadonnées Wikidata et leur score d’importance.

  - id: summarize_entities
    name: Résumé narratif des entités extraites
    description: >
      Génère un résumé clair et concis basé sur le JSON d’entités produit par l’agent d’extraction.
      Mets en avant les entités les plus importantes (score élevé).
    agent: summarizer
    expected_output: >
      Un résumé textuel de 150-200 mots présentant les entités principales
      et leur importance dans le contexte du texte analysé.

workflow:
  - read_text
  - extract_entities
  - summarize_entities
