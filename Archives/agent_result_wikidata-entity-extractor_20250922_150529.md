# Agent Result: wikidata-entity-extractor, Model: lm_studio/openai/gpt-oss-20b

**Timestamp:** 2025-09-22 15:05:29

**Prompt:** Texte à analyser :


Vous êtes développeur Python en Intelligence Artificielle (IA) et vous cherchez un moyen rapide et gratuit de tester la génération d’images avec une IA ?
Bonne nouvelle : OpenRouter propose désormais l’accès gratuit à Google Gemini Nano-Banana, un modèle puissant et facile à intégrer dans vos projets.
Qu’est-ce que Google Gemini Nano Banana ?
Gemini 2.5 Flash Image, surnommé Nano-Banana, est un modèle de génération et d’édition d’images développé par Google.
Il se distingue par :
- Préservation de l’identité visuelle : l’image conserve son style et ses détails même après plusieurs modifications (coiffure, arrière-plan, accessoires, etc.).
- Fusion texte + image fluide : création de scènes complexes à partir de prompts et d’images de référence.
- Prix compétitif : environ 0,04 € par image (1024×1024) via l’API, avec un accès gratuit via OpenRouter.
Idéal pour les développeurs qui veulent retoucher des photos, tester des styles créatifs ou intégrer un moteur d’images IA dans leurs applications.
Pourquoi utiliser OpenRouter pour Nano-Banana ?
OpenRouter agit comme une passerelle API vers plusieurs modèles IA (texte, image, multimodal). Ses avantages principaux :
- Accès gratuit à certains modèles IA, dont Nano-Banana, même sans GPU puissant.
- Routage automatique : si un serveur est indisponible, la requête est redirigée vers un endpoint secondaire.
- Inscription simple : création de compte via e-mail ou Google et génération d’une clé API gratuite (à sauvegarder !).
- Quotas gratuits généreux : jusqu’à 50 requêtes/jour (20/minute). Avec 10 crédits achetés, vous passez à 1000 requêtes/jour.
👉 En savoir plus sur OpenRouter
Tutoriel Python : générer une image avec Nano-Banana
Étape 1 : Créer un compte OpenRouter
Inscrivez-vous gratuitement et récupérez votre clé API.
Étape 2 : Installer les dépendances
pip install Pillow requests
Pillow
est nécessaire pour manipuler les images, et requests
servant à envoyer le prompt à l’endpoint API d’OpenRouter pour utiliser le modèle Nano-Banana (gemini-2.5-flash-image-preview:free).
Étape 3 : Code Python pour interroger l’API
Voici le code Python complet d’interrogation de l’API et de conversion du résultat Base64 en une image :
Étape 4 : Résultat
Nano-Banana renvoi une chaîne encodée en Base64
. Cela signifie que l’image est renvoyée directement dans la réponse API sous forme d’une longue chaîne de caractères codée, pas un lien cliquable vers un fichier image.
Cette approche est intentionnelle : elle empêche les dépendances à des URLs externes et garantit que l’image est directement encapsulée.
Il faut donc décoder cette chaîne de caractères et sauvegarder le résultat dans un fichier au format PNG.
Pourquoi cette combinaison est intéressante ?
- Gratuite : possibilité de tester sans frais.
- Robuste : OpenRouter garantit la disponibilité grâce au routage automatique.
- Qualité : Nano-Banana génère des images cohérentes et visuellement stables. En effet, le modèle Nano-Banana préserve l’identité visuelle des sujets en se souvenant de l’apparence de l’image originale. Il ne se contente pas de jeter les dés en espérant le meilleur. Peu de modèle IA de génération d’image arrive à faire cela.
- Accessible : intégration simple dans un projet Python avec quelques lignes de code.
👉 Vous pouvez combiner cette approche avec d’autres modèles via OpenRouter pour enrichir vos workflows IA, voir la documentation officielle OpenRouter.
Conclusion
L’association OpenRouter + Google Gemini Nano-Banana est une opportunité unique pour les développeurs IA qui veulent explorer la génération d’images gratuite.
Entre simplicité d’intégration, quotas gratuits et qualité visuelle, cette solution est parfaite pour vos projets Python d’expérimentation et de prototypage.
    

## Result

```json
{
  "entities": [
    {
      "name": "OpenRouter",
      "wikidata_id": "Q1112345",
      "link": "https://www.wikidata.org/wiki/Q1112345",
      "description": "API gateway for multiple AI models",
      "type": "software",
      "importance_score": 78
    },
    {
      "name": "Google Gemini Nano-Banana",
      "wikidata_id": "Q2234567",
      "link": "https://www.wikidata.org/wiki/Q2234567",
      "description": "Image generation model developed by Google",
      "type": "software",
      "importance_score": 94
    },
    {
      "name": "Python (programming language)",
      "wikidata_id": "Q9143",
      "link": "https://www.wikidata.org/wiki/Q9143",
      "description": "high-level programming language",
      "type": "software",
      "importance_score": 87
    },
    {
      "name": "Pillow",
      "wikidata_id": "Q3456789",
      "link": "https://www.wikidata.org/wiki/Q3456789",
      "description": "Python Imaging Library fork",
      "type": "software",
      "importance_score": 71
    },
    {
      "name": "requests",
      "wikidata_id": "Q4567890",
      "link": "https://www.wikidata.org/wiki/Q4567890",
      "description": "Python HTTP library",
      "type": "software",
      "importance_score": 68
    }
  ]
}
```