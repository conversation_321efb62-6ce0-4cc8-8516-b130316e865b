../../../bin/ddgs,sha256=k7aH_QEuHppRTYO-pfjdcTCOVAr34rQn1LCVNZ9hwKc,239
ddgs-9.6.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
ddgs-9.6.0.dist-info/METADATA,sha256=hKd-6O1QTK2p9t84uSo-FO7QbSN6NMZdF6pnn94q-qU,18443
ddgs-9.6.0.dist-info/RECORD,,
ddgs-9.6.0.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
ddgs-9.6.0.dist-info/entry_points.txt,sha256=6sJuoKtR53XmVW0vr69w7-wMMKuMuIRsHhIQOBvUxyE,51
ddgs-9.6.0.dist-info/licenses/LICENSE.md,sha256=dnp2N2nTw9zSuJR-S9xX8EFR9-pK28C_E-BSx35gBCc,1063
ddgs-9.6.0.dist-info/top_level.txt,sha256=csdhgnK5-Wv6NZe1OgxAB0ggN6ASxfLrD3Kx_iAiP2M,5
ddgs/__init__.py,sha256=ul0CygGm3wG-G-JR6bDsxS36gMnuXXkZs-BNAQdOMdQ,382
ddgs/__pycache__/__init__.cpython-312.pyc,,
ddgs/__pycache__/base.cpython-312.pyc,,
ddgs/__pycache__/cli.cpython-312.pyc,,
ddgs/__pycache__/ddgs.cpython-312.pyc,,
ddgs/__pycache__/exceptions.cpython-312.pyc,,
ddgs/__pycache__/http_client.cpython-312.pyc,,
ddgs/__pycache__/http_client2.cpython-312.pyc,,
ddgs/__pycache__/results.cpython-312.pyc,,
ddgs/__pycache__/similarity.cpython-312.pyc,,
ddgs/__pycache__/utils.cpython-312.pyc,,
ddgs/base.py,sha256=75x-mILd5xCFuKV9YNtbDGq1arXp5cQskvmkvtK7dm4,4325
ddgs/cli.py,sha256=qaAxTE6xjmwC3PU9xw47_L3vPw1J77GDaOFT7eqeJUQ,17178
ddgs/ddgs.py,sha256=Vy9YD6KjnXFlz5D3CrBMdL0dQ-fBBstY5n5Fpl2ZEuY,9047
ddgs/engines/__init__.py,sha256=BzzxwEgeU175-zausJ_ue3PDPcMQB37JuwKwizWdC8g,3354
ddgs/engines/__pycache__/__init__.cpython-312.pyc,,
ddgs/engines/__pycache__/annasarchive.cpython-312.pyc,,
ddgs/engines/__pycache__/bing.cpython-312.pyc,,
ddgs/engines/__pycache__/bing_news.cpython-312.pyc,,
ddgs/engines/__pycache__/brave.cpython-312.pyc,,
ddgs/engines/__pycache__/duckduckgo.cpython-312.pyc,,
ddgs/engines/__pycache__/duckduckgo_images.cpython-312.pyc,,
ddgs/engines/__pycache__/duckduckgo_news.cpython-312.pyc,,
ddgs/engines/__pycache__/duckduckgo_videos.cpython-312.pyc,,
ddgs/engines/__pycache__/google.cpython-312.pyc,,
ddgs/engines/__pycache__/mojeek.cpython-312.pyc,,
ddgs/engines/__pycache__/mullvad_leta.cpython-312.pyc,,
ddgs/engines/__pycache__/wikipedia.cpython-312.pyc,,
ddgs/engines/__pycache__/yahoo.cpython-312.pyc,,
ddgs/engines/__pycache__/yahoo_news.cpython-312.pyc,,
ddgs/engines/__pycache__/yandex.cpython-312.pyc,,
ddgs/engines/annasarchive.py,sha256=sRgr6U7Eb19MJDAw63MS2WwAAFBoDWtpVqkdNtsTrhQ,1645
ddgs/engines/bing.py,sha256=IMWctk0TDV1Ls_FJo_4nQvhPlzEWZLvAUgfD67hStq0,2664
ddgs/engines/bing_news.py,sha256=pW6jHyE03W0n1i-5tCVUsrfqolWnhor9QGS9UyiSlqc,2822
ddgs/engines/brave.py,sha256=5aABjoAi4fFc5cwR9CMPUHFk2IDNzuQmiyPd02rtRgY,1515
ddgs/engines/duckduckgo.py,sha256=cvs9PwyiTwXaRvuHD7rY3A_mZkv8guwwLZyxWTA7LKI,1409
ddgs/engines/duckduckgo_images.py,sha256=ag67fuz2XvBdYwEUWdGTVREeRK2TSO7pUHSW76YTIeI,3022
ddgs/engines/duckduckgo_news.py,sha256=CCxtJVaumkMZthptFzvhawqdWMfWqi0F-wqxPLqwMwk,2127
ddgs/engines/duckduckgo_videos.py,sha256=UTCxFYt9u3KZfcVr0LFwLmx9AOc-w-LP3NsiJsxp6tI,2844
ddgs/engines/google.py,sha256=Ae89gwbrkeuJfTerh5zt9YkhF_oPBOpruGYVdgKAjkY,2102
ddgs/engines/mojeek.py,sha256=07KklOHSNe9pTjuEzcrXS_oxdygyvbZt3mhKCUpzGjY,1470
ddgs/engines/mullvad_leta.py,sha256=UFqhljMV1MjJ0LSUObI9VJ_UnFzUvHB5R4EhfHNaA_s,2149
ddgs/engines/wikipedia.py,sha256=kZvclXEQHoa36cCqahWnD1L7_gGGpWhg496-1rJK9l4,2003
ddgs/engines/yahoo.py,sha256=l8G1YrIvDOibtvPvrnMEeJiXDoR-Lx9fnNZsp-NYCCk,1922
ddgs/engines/yahoo_news.py,sha256=tIsNxAzWAk3ixoRwDWFByCxfmB9DGyDzzDLY1Tvgw4I,3079
ddgs/engines/yandex.py,sha256=KwE9Or0F9aUcRAEu8J7bEQhx0fGqxkjSuYU_p6uPAkE,1147
ddgs/exceptions.py,sha256=C1ufiGwK3NpXKRb3Dlsaoy0n_xnSnfnYDqm6ta-0xvs,308
ddgs/http_client.py,sha256=f5TbflDDK3dkBz3sV9aU-txhNtl6HWCfzFTttP5e2kI,3032
ddgs/http_client2.py,sha256=cPbI2SFa2KFJH6JtxoxMTTM1UnHrq_V-Q97QFwNbbCI,5958
ddgs/py.typed,sha256=dcrsqJrcYfTX-ckLFJMTaj6mD8aDe2u0tkQG-ZYxnEg,26
ddgs/results.py,sha256=4wknAhgMEpGoGa2Va7LgZxOHlGjVUZ_rvWzGsPdnALk,4199
ddgs/similarity.py,sha256=azVAKv7VVjbrZ22414-HadPg5NKE9dAgLK76LuVOqo8,2298
ddgs/utils.py,sha256=mdcSrEl8WYOT7iU5hIqqwM-IZ7lu_JyyklDPeDR4z0Q,2775
